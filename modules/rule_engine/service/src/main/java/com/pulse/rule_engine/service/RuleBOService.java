package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.pulse.common.enums.VersionChangeType;
import com.pulse.pulse.common.utils.SemanticVersion;
import com.pulse.pulse.common.utils.SemanticVersionUtils;
import com.pulse.rule_engine.common.enums.RuleVersionChangeTypeEnum;
import com.pulse.rule_engine.manager.bo.RuleBO;
import com.pulse.rule_engine.manager.bo.RuleOrganizationBO;
import com.pulse.rule_engine.manager.bo.RuleVersionBO;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.Rule;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.persist.qto.ListRuleVersionQto;
import com.pulse.rule_engine.service.base.BaseRuleBOService;
import com.pulse.rule_engine.service.bto.DeleteRuleBto;
import com.pulse.rule_engine.service.bto.MergeRuleBto;
import com.pulse.rule_engine.service.query.RuleVersionBaseDtoQueryService;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|SERVICE")
public class RuleBOService extends BaseRuleBOService {
    @AutoGenerated(locked = true)
    @Resource
    private RuleBaseDtoService ruleBaseDtoService;

    @Resource private RuleVersionBaseDtoQueryService ruleVersionBaseDtoQueryService;

    /** 删除规则（逻辑删除） */
    @PublicInterface(id = "5ddb0834-c4aa-4f2e-a887-5317314a1f1d", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "1417736a-430e-4a89-90d3-2de45e738eb4")
    public String deleteRule(@Valid @NotNull DeleteRuleBto deleteRuleBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleBaseDto ruleBaseDto = ruleBaseDtoService.getById(deleteRuleBto.getId());
        DeleteRuleBoResult boResult = super.deleteRuleBase(deleteRuleBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteRuleBto */
        {
            DeleteRuleBto bto =
                    boResult.<DeleteRuleBto>getBtoOfType(DeleteRuleBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteRuleBto, Rule> deletedBto = boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存规则，包括规则基础信息、规则版本、规则组织信息 */
    @PublicInterface(id = "01383b4b-9202-4caf-b70f-56f83c18ff42", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "6166d994-64ba-4fcd-a88b-265827f89f67")
    public String mergeRule(@Valid @NotNull MergeRuleBto mergeRuleBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleBaseDto ruleBaseDto = null;
        if (mergeRuleBto.getId() != null) {
            ruleBaseDto = ruleBaseDtoService.getById(mergeRuleBto.getId());
        }
        MergeRuleBoResult boResult = super.mergeRuleBase(mergeRuleBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeRuleBto.RuleVersionBto */
        {
            // 验证规则版本数量限制
            validateRuleVersionCount(mergeRuleBto);

            for (MergeRuleBto.RuleVersionBto bto :
                    boResult.<MergeRuleBto.RuleVersionBto>getBtoOfType(
                            MergeRuleBto.RuleVersionBto.class)) {
                UpdatedBto<MergeRuleBto.RuleVersionBto, RuleVersion, RuleVersionBO> updatedBto =
                        boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeRuleBto.RuleVersionBto, RuleVersionBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    RuleVersionBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    RuleVersion entity = updatedBto.getEntity();
                    // 其他自定义操作...
                    // 规则版本只能被新增，不能被更新
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则版本不允许更新，只能新增新版本");
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    RuleVersionBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    // 处理规则版本新增逻辑
                    processRuleVersionCreation(bo, mergeRuleBto, boResult);
                }
            }
        }
        /** 处理 MergeRuleBto.RuleOrganizationBto */
        {
            for (MergeRuleBto.RuleOrganizationBto bto :
                    boResult.<MergeRuleBto.RuleOrganizationBto>getBtoOfType(
                            MergeRuleBto.RuleOrganizationBto.class)) {
                UpdatedBto<MergeRuleBto.RuleOrganizationBto, RuleOrganization, RuleOrganizationBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeRuleBto.RuleOrganizationBto, RuleOrganizationBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    RuleOrganizationBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    RuleOrganization entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    RuleOrganizationBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<RuleOrganization> deletedEntityList =
                    boResult.getDeletedEntityList(RuleOrganization.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 MergeRuleBto */
        {
            MergeRuleBto bto =
                    boResult.<MergeRuleBto>getBtoOfType(MergeRuleBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeRuleBto, Rule, RuleBO> updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeRuleBto, RuleBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                RuleBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                Rule entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                RuleBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /**
     * 处理规则版本创建逻辑
     *
     * @param ruleVersionBO 待创建的规则版本BO
     * @param mergeRuleBto 合并规则BTO
     * @param boResult 合并结果
     */
    private void processRuleVersionCreation(
            RuleVersionBO ruleVersionBO, MergeRuleBto mergeRuleBto, MergeRuleBoResult boResult) {
        // 如果规则版本为空，则不处理
        if (ruleVersionBO == null) {
            log.debug("规则版本为空，跳过处理");
            return;
        }

        // 获取规则BO
        RuleBO ruleBO = boResult.getRootBo();
        if (ruleBO == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则信息不能为空");
        }

        // 如果规则版本列表为空，则不处理
        if (CollectionUtil.isEmpty(mergeRuleBto.getRuleVersionBtoList())) {
            log.debug("规则版本列表为空，跳过处理");
            return;
        }

        // 获取当前规则的最新版本
        RuleVersionBaseDto latestVersion = getLatestRuleVersion(ruleBO.getId());

        // 如果存在最新版本，检查是否有变化
        if (latestVersion != null) {
            boolean hasChanges = hasRuleVersionChanges(ruleVersionBO, latestVersion);
            if (!hasChanges) {
                log.info("规则版本内容无变化，跳过新增版本。规则ID: {}", ruleBO.getId());
                // 移除这个版本BO，不进行保存
                ruleBO.getRuleVersionBOSet().remove(ruleVersionBO);
                return;
            }
        }

        // 生成新版本号
        String newVersionNumber =
                generateNewVersionNumber(latestVersion, mergeRuleBto.getRuleVersionChangeType());
        ruleVersionBO.setVersionNumber(newVersionNumber);

        log.info("创建新规则版本: 规则ID={}, 新版本号={}", ruleBO.getId(), newVersionNumber);

        // 更新规则的当前版本
        ruleBO.setCurrentVersion(newVersionNumber);
    }

    /**
     * 获取规则的最新版本 使用RuleVersionBaseDtoQueryService的listRuleVersionPaged方法获取最新版本的规则版本记录
     *
     * @param ruleId 规则ID
     * @return 最新版本DTO，如果没有版本则返回null
     */
    private RuleVersionBaseDto getLatestRuleVersion(String ruleId) {
        if (StrUtil.isBlank(ruleId)) {
            return null;
        }

        try {
            // 构建查询条件，获取最新的一个版本
            ListRuleVersionQto qto = new ListRuleVersionQto();
            qto.setRuleIdIs(ruleId);
            qto.setFrom(0);
            qto.setSize(1); // 只获取最新的一个版本

            // 使用查询服务获取分页结果（已按version_number desc, created_at desc排序，并完成数据装配）
            VSQueryResult<RuleVersionBaseDto> queryResult =
                    ruleVersionBaseDtoQueryService.listRuleVersionPaged(qto);

            if (queryResult == null || CollectionUtil.isEmpty(queryResult.getResult())) {
                log.debug("规则ID: {} 没有找到任何版本", ruleId);
                return null;
            }

            // 获取最新版本（第一个就是最新的）
            RuleVersionBaseDto latestVersion = queryResult.getResult().get(0);

            if (latestVersion != null) {
                log.debug(
                        "获取到规则最新版本: 规则ID={}, 版本号={}, 版本ID={}, 总版本数={}",
                        ruleId,
                        latestVersion.getVersionNumber(),
                        latestVersion.getId(),
                        queryResult.getCount());
            }

            return latestVersion;
        } catch (Exception e) {
            log.error("获取规则最新版本失败，规则ID: {}", ruleId, e);
            return null;
        }
    }

    /**
     * 检查规则版本是否有变化
     *
     * @param newRuleVersion 新的规则版本BO
     * @param latestVersion 最新的规则版本DTO
     * @return true表示有变化，false表示无变化
     */
    private boolean hasRuleVersionChanges(
            RuleVersionBO newRuleVersion, RuleVersionBaseDto latestVersion) {
        if (newRuleVersion == null || latestVersion == null) {
            return true; // 如果任一为空，认为有变化
        }

        // 比较DRL内容
        String newDrlContent = StrUtil.nullToEmpty(newRuleVersion.getDrlContent()).trim();
        String latestDrlContent = StrUtil.nullToEmpty(latestVersion.getDrlContent()).trim();

        if (!Objects.equals(newDrlContent, latestDrlContent)) {
            log.debug("DRL内容有变化");
            return true;
        }

        // 比较生效开始时间
        if (!Objects.equals(
                newRuleVersion.getEffectiveStartTime(), latestVersion.getEffectiveStartTime())) {
            log.debug("生效开始时间有变化");
            return true;
        }

        // 比较生效结束时间
        if (!Objects.equals(
                newRuleVersion.getEffectiveEndTime(), latestVersion.getEffectiveEndTime())) {
            log.debug("生效结束时间有变化");
            return true;
        }

        log.debug("规则版本内容无变化");
        return false;
    }

    /**
     * 生成新版本号
     *
     * @param latestVersion 最新版本DTO
     * @param changeType 版本变更类型
     * @return 新版本号
     */
    private String generateNewVersionNumber(
            RuleVersionBaseDto latestVersion, RuleVersionChangeTypeEnum changeType) {
        // 如果没有最新版本，创建初始版本
        if (latestVersion == null || StrUtil.isBlank(latestVersion.getVersionNumber())) {
            return SemanticVersionUtils.createInitialVersion().toString();
        }

        // 如果没有指定变更类型，默认使用PATCH
        if (changeType == null) {
            changeType = RuleVersionChangeTypeEnum.PATCH;
            log.warn("未指定版本变更类型，默认使用PATCH");
        }

        try {
            // 转换枚举类型
            VersionChangeType versionChangeType = convertToVersionChangeType(changeType);

            // 生成新版本号
            SemanticVersion newVersion =
                    SemanticVersionUtils.incrementVersion(
                            latestVersion.getVersionNumber(), versionChangeType);

            return newVersion.toString();
        } catch (Exception e) {
            log.error(
                    "生成新版本号失败，当前版本: {}, 变更类型: {}", latestVersion.getVersionNumber(), changeType, e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "生成新版本号失败: " + e.getMessage());
        }
    }

    /**
     * 将RuleVersionChangeTypeEnum转换为VersionChangeType
     *
     * @param ruleVersionChangeType 规则版本变更类型枚举
     * @return 版本变更类型枚举
     */
    private VersionChangeType convertToVersionChangeType(
            RuleVersionChangeTypeEnum ruleVersionChangeType) {
        if (ruleVersionChangeType == null) {
            return VersionChangeType.PATCH;
        }

        switch (ruleVersionChangeType) {
            case MAJOR:
                return VersionChangeType.MAJOR;
            case MINOR:
                return VersionChangeType.MINOR;
            case PATCH:
                return VersionChangeType.PATCH;
            default:
                log.warn("未知的版本变更类型: {}, 默认使用PATCH", ruleVersionChangeType);
                return VersionChangeType.PATCH;
        }
    }

    /**
     * 验证规则版本数量限制 从入参MergeRuleBto中获取规则版本列表并验证数量
     *
     * @param mergeRuleBto 合并规则BTO
     * @throws IgnoredException 如果规则版本数量超过限制
     */
    private void validateRuleVersionCount(MergeRuleBto mergeRuleBto) {
        if (mergeRuleBto == null) {
            return;
        }

        // 从入参MergeRuleBto中获取规则版本列表
        List<MergeRuleBto.RuleVersionBto> ruleVersionBtoList = mergeRuleBto.getRuleVersionBtoList();

        // 检查规则版本数量限制：当前方法只支持新增一个规则版本
        if (CollectionUtil.isNotEmpty(ruleVersionBtoList) && ruleVersionBtoList.size() > 1) {
            log.warn("规则版本数量超过限制: 传入了{}个版本，但当前方法只支持新增一个规则版本", ruleVersionBtoList.size());
            throw new IgnoredException(
                    ErrorCode.WRONG_PARAMETER,
                    "当前方法只支持新增一个规则版本，传入了" + ruleVersionBtoList.size() + "个版本");
        }

        if (CollectionUtil.isNotEmpty(ruleVersionBtoList)) {
            log.debug("规则版本数量验证通过: 传入了{}个版本", ruleVersionBtoList.size());
        }
    }
}
